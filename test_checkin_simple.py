#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试签到功能的逻辑
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from auth import UserManager
from points_system import PointsSystem
from beijing_time import beijing_now, format_beijing_time

def test_checkin_logic():
    """测试签到逻辑"""
    print("=== 测试签到逻辑 ===")
    
    # 初始化系统
    user_manager = UserManager('test_users.json')
    points_system = PointsSystem('test_points.json')
    
    # 创建测试用户
    test_username = "test_user"
    if test_username not in user_manager.users:
        success, message = user_manager.register_user(test_username, "password123", "<EMAIL>")
        if success:
            print(f"✓ 创建测试用户成功: {message}")
        else:
            print(f"✗ 创建测试用户失败: {message}")
            return
    else:
        print(f"✓ 使用现有测试用户: {test_username}")
    
    user = user_manager.get_user(test_username)
    print(f"  用户当前积分: {user['points']}")
    
    # 获取今日日期
    today = format_beijing_time(beijing_now(), '%Y-%m-%d')
    print(f"  今日日期: {today}")
    
    # 检查签到状态
    last_checkin = user.get('last_checkin_date', '')
    print(f"  上次签到日期: {last_checkin}")
    
    if last_checkin == today:
        print("  今日已签到")
    else:
        print("  今日未签到，可以签到")
        
        # 执行签到
        checkin_points = points_system.get_settings().get('daily_bonus', 100)
        print(f"  签到奖励积分: {checkin_points}")
        
        # 更新用户积分
        success, message = user_manager.update_user_points(test_username, checkin_points)
        if success:
            print(f"✓ 积分更新成功: {message}")
        else:
            print(f"✗ 积分更新失败: {message}")
            return
        
        # 更新签到记录
        user['last_checkin_date'] = today
        user['total_checkins'] = user.get('total_checkins', 0) + 1
        user_manager.save_users()
        
        # 记录积分交易
        points_system.add_transaction(test_username, checkin_points, 'checkin', '每日签到奖励')
        
        # 获取更新后的用户信息
        updated_user = user_manager.get_user(test_username)
        print(f"✓ 签到成功！")
        print(f"  获得积分: {checkin_points}")
        print(f"  当前积分: {updated_user['points']}")
        print(f"  总签到次数: {updated_user.get('total_checkins', 1)}")
    
    # 测试重复签到
    print("\n--- 测试重复签到 ---")
    last_checkin = user_manager.get_user(test_username).get('last_checkin_date', '')
    if last_checkin == today:
        print("✓ 重复签到被正确阻止")
    else:
        print("✗ 重复签到检查失败")
    
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    test_checkin_logic()
