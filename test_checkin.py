#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试签到功能
"""

import requests
import json

# 测试配置
BASE_URL = "http://localhost:7799"
TEST_USERNAME = "admin"
TEST_PASSWORD = "admin123"

def test_checkin_functionality():
    """测试签到功能"""
    session = requests.Session()
    
    print("=== 测试签到功能 ===")
    
    # 1. 登录
    print("1. 登录测试用户...")
    login_data = {
        "username": TEST_USERNAME,
        "password": TEST_PASSWORD
    }
    
    response = session.post(f"{BASE_URL}/login", json=login_data)
    if response.status_code == 200:
        result = response.json()
        if result.get('success'):
            print(f"✓ 登录成功: {result.get('message')}")
            user_info = result.get('user', {})
            print(f"  当前积分: {user_info.get('points', 0)}")
        else:
            print(f"✗ 登录失败: {result.get('message')}")
            return
    else:
        print(f"✗ 登录请求失败: {response.status_code}")
        return
    
    # 2. 检查签到状态
    print("\n2. 检查签到状态...")
    response = session.get(f"{BASE_URL}/checkin_status")
    if response.status_code == 200:
        result = response.json()
        if result.get('success'):
            has_checked_in = result.get('has_checked_in', False)
            total_checkins = result.get('total_checkins', 0)
            print(f"✓ 签到状态查询成功")
            print(f"  今日已签到: {'是' if has_checked_in else '否'}")
            print(f"  总签到次数: {total_checkins}")
        else:
            print(f"✗ 签到状态查询失败: {result.get('message')}")
    else:
        print(f"✗ 签到状态查询请求失败: {response.status_code}")
    
    # 3. 执行签到
    print("\n3. 执行签到...")
    response = session.get(f"{BASE_URL}/daily_checkin")
    if response.status_code == 200:
        result = response.json()
        if result.get('success'):
            points_gained = result.get('points_gained', 0)
            new_points = result.get('new_points', 0)
            total_checkins = result.get('total_checkins', 0)
            print(f"✓ 签到成功: {result.get('message')}")
            print(f"  获得积分: {points_gained}")
            print(f"  当前积分: {new_points}")
            print(f"  总签到次数: {total_checkins}")
        else:
            print(f"✗ 签到失败: {result.get('message')}")
    else:
        print(f"✗ 签到请求失败: {response.status_code}")
    
    # 4. 再次检查签到状态
    print("\n4. 再次检查签到状态...")
    response = session.get(f"{BASE_URL}/checkin_status")
    if response.status_code == 200:
        result = response.json()
        if result.get('success'):
            has_checked_in = result.get('has_checked_in', False)
            total_checkins = result.get('total_checkins', 0)
            print(f"✓ 签到状态查询成功")
            print(f"  今日已签到: {'是' if has_checked_in else '否'}")
            print(f"  总签到次数: {total_checkins}")
        else:
            print(f"✗ 签到状态查询失败: {result.get('message')}")
    else:
        print(f"✗ 签到状态查询请求失败: {response.status_code}")
    
    # 5. 尝试重复签到
    print("\n5. 尝试重复签到...")
    response = session.get(f"{BASE_URL}/daily_checkin")
    if response.status_code == 200:
        result = response.json()
        if not result.get('success'):
            print(f"✓ 重复签到被正确阻止: {result.get('message')}")
        else:
            print(f"✗ 重复签到未被阻止，这是一个问题")
    else:
        print(f"✗ 重复签到请求失败: {response.status_code}")
    
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    test_checkin_functionality()
